<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信扫码登录 - 滴答清单API</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .login-container {
            background: white;
            padding: 32px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            text-align: center;
            max-width: 350px;
            width: 100%;
        }

        .logo {
            width: 48px;
            height: 48px;
            margin: 0 auto 16px;
            background: #2563eb;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: 600;
        }

        h1 {
            color: #1f2937;
            margin-bottom: 6px;
            font-size: 20px;
            font-weight: 600;
        }

        .subtitle {
            color: #6b7280;
            margin-bottom: 24px;
            font-size: 14px;
        }

        .qr-container {
            margin: 24px 0;
            min-height: 180px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .qr-image {
            width: 180px;
            height: 180px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            background: #fafafa;
        }

        .loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            color: #6b7280;
            font-size: 14px;
        }

        .spinner {
            width: 24px;
            height: 24px;
            border: 2px solid #e5e7eb;
            border-top: 2px solid #2563eb;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-message {
            margin: 16px 0;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
        }

        .status-waiting {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }

        .status-scanned {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #bfdbfe;
        }

        .status-success {
            background: #d1fae5;
            color: #047857;
            border: 1px solid #a7f3d0;
        }

        .status-error {
            background: #fee2e2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        .refresh-btn {
            background: #2563eb;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.15s;
        }

        .refresh-btn:hover {
            background: #1d4ed8;
        }

        .refresh-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        .user-info {
            margin-top: 16px;
            padding: 12px;
            background: #f9fafb;
            border-radius: 6px;
            text-align: left;
        }

        .user-info h3 {
            color: #1f2937;
            margin-bottom: 6px;
            font-size: 14px;
            font-weight: 600;
        }

        .user-info p {
            color: #6b7280;
            font-size: 13px;
            margin: 2px 0;
        }

        .hidden {
            display: none !important;
        }

        .steps {
            margin: 20px 0 0 0;
            font-size: 12px;
            color: #9ca3af;
            text-align: left;
        }

        .steps ol {
            padding-left: 16px;
        }

        .steps li {
            margin: 4px 0;
            line-height: 1.4;
        }

        .footer {
            margin-top: 20px;
            padding-top: 16px;
            border-top: 1px solid #e5e7eb;
            color: #9ca3af;
            font-size: 12px;
        }

        .footer a {
            color: #2563eb;
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">滴答</div>
        <h1>微信扫码登录</h1>
        <p class="subtitle">使用微信扫描二维码完成登录</p>

        <div class="qr-container">
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <span>正在获取二维码...</span>
            </div>
            
            <div id="qr-display" class="hidden">
                <img id="qr-image" class="qr-image" alt="微信二维码" />
            </div>
        </div>

        <div id="status-message" class="status-message hidden"></div>

        <div id="refresh-container" class="hidden">
            <button id="refresh-btn" class="refresh-btn" onclick="refreshQRCode()">
                刷新二维码
            </button>
        </div>

        <div id="user-info" class="user-info hidden">
            <h3>登录成功</h3>
            <p id="user-username"></p>
            <p id="user-email"></p>
        </div>

        <div class="steps">
            <ol>
                <li>打开微信扫一扫</li>
                <li>扫描二维码</li>
                <li>手机确认登录</li>
                <li>自动跳转</li>
            </ol>
        </div>

        <div class="footer">
            <p>滴答清单API · <a href="/docs" target="_blank">接口文档</a></p>
        </div>
    </div>

    <script>
        const API_BASE = window.location.origin;
        let pollInterval = null;
        let currentQRKey = null;

        // 状态枚举
        const STATUS = {
            LOADING: 'loading',
            WAITING: 'waiting',
            SCANNED: 'scanned', 
            SUCCESS: 'success',
            ERROR: 'error',
            EXPIRED: 'expired'
        };

        // 显示状态消息
        function showStatus(status, message) {
            const statusEl = document.getElementById('status-message');
            const refreshContainer = document.getElementById('refresh-container');
            
            statusEl.className = `status-message status-${status}`;
            statusEl.textContent = message;
            statusEl.classList.remove('hidden');

            // 错误或过期状态显示刷新按钮
            if (status === STATUS.ERROR || status === STATUS.EXPIRED) {
                refreshContainer.classList.remove('hidden');
            } else {
                refreshContainer.classList.add('hidden');
            }
        }

        // 隐藏状态消息
        function hideStatus() {
            document.getElementById('status-message').classList.add('hidden');
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').classList.remove('hidden');
            document.getElementById('qr-display').classList.add('hidden');
        }

        // 显示二维码
        function showQRCode(qrCodeUrl) {
            const qrImage = document.getElementById('qr-image');
            qrImage.src = qrCodeUrl;
            qrImage.onload = function() {
                document.getElementById('loading').classList.add('hidden');
                document.getElementById('qr-display').classList.remove('hidden');
                showStatus(STATUS.WAITING, '请使用微信扫描二维码');
            };
            qrImage.onerror = function() {
                showStatus(STATUS.ERROR, '二维码加载失败，请重新获取');
            };
        }

        // 显示用户信息
        function showUserInfo(userInfo) {
            const userInfoEl = document.getElementById('user-info');
            const usernameEl = document.getElementById('user-username');
            const emailEl = document.getElementById('user-email');

            if (userInfo.username) {
                usernameEl.textContent = `用户名: ${userInfo.username}`;
            }
            if (userInfo.email) {
                emailEl.textContent = `邮箱: ${userInfo.email}`;
            }

            userInfoEl.classList.remove('hidden');
        }

        // 获取微信二维码
        async function getWeChatQRCode() {
            try {
                showLoading();
                hideStatus();

                const response = await fetch(`${API_BASE}/auth/wechat/qrcode?state=web_login`);
                const data = await response.json();

                if (response.ok && data.qr_code_key && data.qr_code_url) {
                    currentQRKey = data.qr_code_key;
                    showQRCode(data.qr_code_url);
                    startPolling(currentQRKey);
                } else {
                    throw new Error(data.message || data.detail || '获取二维码失败');
                }
            } catch (error) {
                console.error('获取二维码失败:', error);
                showStatus(STATUS.ERROR, `获取二维码失败: ${error.message}`);
            }
        }

        // 开始轮询登录状态
        function startPolling(qrCodeKey) {
            if (pollInterval) {
                clearInterval(pollInterval);
            }

            pollInterval = setInterval(async () => {
                try {
                    const response = await fetch(`${API_BASE}/auth/wechat/poll?qr_code_key=${qrCodeKey}&max_attempts=1`);
                    
                    if (response.ok) {
                        const data = await response.json();
                        
                        // 检查是否登录成功
                        if (data.success) {
                            // 登录成功
                            clearInterval(pollInterval);
                            showStatus(STATUS.SUCCESS, '登录成功！正在跳转...');
                            
                            if (data.user_info) {
                                showUserInfo(data.user_info);
                            }

                            // 保存认证信息到 localStorage
                            if (data.session_info) {
                                localStorage.setItem('auth_token', data.session_info.auth_token);
                                localStorage.setItem('csrf_token', data.session_info.csrf_token);
                            }

                            // 3秒后跳转到主页面
                            setTimeout(() => {
                                window.location.href = `${API_BASE}/docs`;
                            }, 3000);
                            return;
                        }
                        
                        // 检查错误消息
                        const message = data.message || data.detail || '';
                        if (message.includes('等待确认') || message.includes('已扫码')) {
                            // 已扫码，等待确认
                            showStatus(STATUS.SCANNED, '已扫码，请在手机上确认登录');
                        } else if (message.includes('过期') || message.includes('失效')) {
                            // 二维码过期
                            clearInterval(pollInterval);
                            showStatus(STATUS.EXPIRED, '二维码已过期，请重新获取');
                        } else if (message.includes('等待扫码') || message.includes('等待')) {
                            // 仍在等待扫码，保持当前状态
                            // showStatus(STATUS.WAITING, '请使用微信扫描二维码');
                        }
                    } else {
                        // HTTP错误，但不停止轮询
                        console.log('轮询HTTP错误:', response.status);
                    }
                } catch (error) {
                    console.error('轮询状态失败:', error);
                    // 网络错误不停止轮询，只是继续等待
                }
            }, 3000); // 每3秒轮询一次

            // 5分钟后自动停止轮询
            setTimeout(() => {
                if (pollInterval) {
                    clearInterval(pollInterval);
                    showStatus(STATUS.EXPIRED, '登录超时，请重新获取二维码');
                }
            }, 300000);
        }

        // 刷新二维码
        function refreshQRCode() {
            if (pollInterval) {
                clearInterval(pollInterval);
            }
            getWeChatQRCode();
        }

        // 页面加载完成后自动获取二维码
        document.addEventListener('DOMContentLoaded', function() {
            getWeChatQRCode();
        });

        // 页面离开时清理轮询
        window.addEventListener('beforeunload', function() {
            if (pollInterval) {
                clearInterval(pollInterval);
            }
        });
    </script>
</body>
</html> 