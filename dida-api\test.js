/**
 * 滴答清单API云对象测试脚本
 * 用于测试各个功能模块的基本功能
 *
 * 测试覆盖范围：
 * - 模块加载测试
 * - 配置验证测试
 * - 工具函数测试
 * - 参数验证测试
 * - 错误处理测试
 * - 性能基准测试
 */

// 注意：这个测试脚本需要在uniCloud环境中运行
// 在本地开发环境中，需要模拟uniCloud.httpclient

// 测试统计信息
const testStats = {
	total: 0,
	passed: 0,
	failed: 0,
	errors: [],
};

/**
 * 测试断言函数
 * @param {boolean} condition - 测试条件
 * @param {string} message - 测试消息
 */
function assert(condition, message) {
	testStats.total++;
	if (condition) {
		testStats.passed++;
		console.log(`✅ ${message}`);
	} else {
		testStats.failed++;
		testStats.errors.push(message);
		console.error(`❌ ${message}`);
	}
}

/**
 * 性能测试函数
 * @param {Function} fn - 要测试的函数
 * @param {string} name - 测试名称
 */
async function performanceTest(fn, name) {
	const start = Date.now();
	try {
		await fn();
		const duration = Date.now() - start;
		console.log(`⏱️  ${name}: ${duration}ms`);
		return duration;
	} catch (error) {
		const duration = Date.now() - start;
		console.error(`⏱️  ${name}: ${duration}ms (错误: ${error.message})`);
		return duration;
	}
}

/**
 * 测试认证模块
 */
async function testAuthModule() {
	console.log("=== 开始测试认证模块 ===");

	try {
		// 测试获取微信登录二维码
		console.log("1. 测试获取微信登录二维码...");
		// const qrResult = await authModule.getWeChatQRCode()
		// console.log('二维码结果:', qrResult)

		// 测试参数验证
		console.log("2. 测试参数验证...");
		// const invalidResult = await authModule.pollQRStatus(null)
		// console.log('参数验证结果:', invalidResult)

		console.log("认证模块测试完成");
	} catch (error) {
		console.error("认证模块测试失败:", error);
	}
}

/**
 * 测试工具函数
 */
function testUtils() {
	console.log("=== 开始测试工具函数 ===");

	const utils = require("./utils");

	try {
		// 测试日志功能
		console.log("1. 测试日志功能...");
		utils.logInfo("test", "这是一条测试信息");
		utils.logWarn("test", "这是一条测试警告");
		utils.logError("test", "这是一条测试错误");
		assert(typeof utils.logInfo === "function", "日志函数 logInfo 存在");
		assert(typeof utils.logWarn === "function", "日志函数 logWarn 存在");
		assert(typeof utils.logError === "function", "日志函数 logError 存在");

		// 测试响应创建
		console.log("2. 测试响应创建...");
		const successResponse = utils.createSuccessResponse("操作成功", {
			data: "test",
		});
		assert(successResponse.errCode === null, "成功响应的 errCode 为 null");
		assert(successResponse.errMsg === "操作成功", "成功响应的 errMsg 正确");
		assert(successResponse.data.data === "test", "成功响应的 data 正确");
		assert(typeof successResponse.timestamp === "number", "成功响应包含时间戳");

		const errorResponse = utils.createErrorResponse("TEST_ERROR", "测试错误");
		assert(errorResponse.errCode === "TEST_ERROR", "错误响应的 errCode 正确");
		assert(errorResponse.errMsg === "测试错误", "错误响应的 errMsg 正确");
		assert(typeof errorResponse.timestamp === "number", "错误响应包含时间戳");

		// 测试参数验证
		console.log("3. 测试参数验证...");
		const nullValidation = utils.validateRequired(null, "testParam");
		assert(nullValidation !== null, "空参数验证返回错误");
		assert(
			nullValidation.errCode === "PARAM_IS_NULL",
			"空参数验证错误代码正确"
		);

		const typeValidation = utils.validateType("string", "number", "testParam");
		assert(typeValidation !== null, "类型验证返回错误");
		assert(
			typeValidation.errCode === "PARAM_TYPE_ERROR",
			"类型验证错误代码正确"
		);

		const emailValidation = utils.validateEmail("invalid-email", "email");
		assert(emailValidation !== null, "邮箱验证返回错误");
		assert(
			emailValidation.errCode === "PARAM_FORMAT_ERROR",
			"邮箱验证错误代码正确"
		);

		const validEmailValidation = utils.validateEmail(
			"<EMAIL>",
			"email"
		);
		assert(validEmailValidation === null, "有效邮箱验证通过");

		// 测试时间处理
		console.log("4. 测试时间处理...");
		const duration = utils.formatDuration(3661);
		assert(duration === "1小时1分1秒", "时长格式化正确");

		const shortDuration = utils.formatDuration(30);
		assert(shortDuration === "30秒", "短时长格式化正确");

		const minuteDuration = utils.formatDuration(90);
		assert(minuteDuration === "1分30秒", "分钟时长格式化正确");

		// 测试数据处理
		console.log("5. 测试数据处理...");
		const testObj = { a: 1, b: { c: 2 } };
		const cloned = utils.deepClone(testObj);
		assert(
			JSON.stringify(cloned) === JSON.stringify(testObj),
			"深度克隆结果正确"
		);
		assert(cloned !== testObj, "深度克隆创建了新对象");
		assert(cloned.b !== testObj.b, "深度克隆创建了新的嵌套对象");

		const jsonResult = utils.safeJsonParse('{"test": "value"}');
		assert(jsonResult.test === "value", "JSON解析正确");

		const invalidJsonResult = utils.safeJsonParse("invalid json", {
			default: true,
		});
		assert(invalidJsonResult.default === true, "无效JSON解析返回默认值");

		console.log("工具函数测试完成");
	} catch (error) {
		console.error("工具函数测试失败:", error);
	}
}

/**
 * 测试配置模块
 */
function testConfig() {
	console.log("=== 开始测试配置模块 ===");

	const config = require("./config");

	try {
		// 测试URL构建
		console.log("1. 测试URL构建...");
		const qrUrl = config.buildWeChatQRUrl();
		assert(qrUrl.includes("open.weixin.qq.com"), "微信二维码URL包含正确域名");
		assert(qrUrl.includes("appid="), "微信二维码URL包含appid参数");
		assert(qrUrl.includes("scope=snsapi_login"), "微信二维码URL包含正确scope");

		const pollUrl = config.buildWeChatPollUrl("test-uuid");
		assert(pollUrl.includes("long.open.weixin.qq.com"), "轮询URL包含正确域名");
		assert(pollUrl.includes("uuid=test-uuid"), "轮询URL包含正确uuid");

		const apiUrl = config.buildDidaApiUrl("/test/endpoint");
		assert(
			apiUrl === "https://api.dida365.com/api/v2/test/endpoint",
			"API URL构建正确"
		);

		const validateUrl = config.buildWeChatValidateUrl("test-code");
		assert(validateUrl.includes("code=test-code"), "验证URL包含正确code");

		const loginUrl = config.buildPasswordLoginUrl();
		assert(loginUrl.includes("wc=true"), "登录URL包含wc参数");
		assert(loginUrl.includes("remember=true"), "登录URL包含remember参数");

		// 测试配置常量
		console.log("2. 测试配置常量...");
		assert(typeof config.WECHAT_CONFIG === "object", "微信配置存在");
		assert(
			config.WECHAT_CONFIG.app_id === "wxf1429a73d311aad4",
			"微信应用ID正确"
		);
		assert(config.WECHAT_CONFIG.scope === "snsapi_login", "微信授权范围正确");

		assert(typeof config.DIDA_API_BASE === "object", "API基础配置存在");
		assert(
			config.DIDA_API_BASE.base_url === "https://api.dida365.com/api/v2",
			"API基础URL正确"
		);

		assert(typeof config.ERROR_CODES === "object", "错误代码配置存在");
		assert(
			config.ERROR_CODES.PARAM_IS_NULL === "PARAM_IS_NULL",
			"错误代码定义正确"
		);

		// 测试API端点配置
		console.log("3. 测试API端点配置...");
		assert(typeof config.DIDA_AUTH_APIS === "object", "认证API配置存在");
		assert(typeof config.DIDA_TASK_APIS === "object", "任务API配置存在");
		assert(typeof config.DIDA_POMODORO_APIS === "object", "专注API配置存在");
		assert(typeof config.DIDA_PROJECT_APIS === "object", "项目API配置存在");
		assert(typeof config.DIDA_HABIT_APIS === "object", "习惯API配置存在");
		assert(typeof config.DIDA_STATISTICS_APIS === "object", "统计API配置存在");
		assert(typeof config.DIDA_USER_APIS === "object", "用户API配置存在");

		console.log("配置模块测试完成");
	} catch (error) {
		console.error("配置模块测试失败:", error);
	}
}

/**
 * 性能基准测试
 */
async function performanceBenchmark() {
	console.log("=== 开始性能基准测试 ===");

	const utils = require("./utils");
	const config = require("./config");

	try {
		// 测试工具函数性能
		console.log("1. 测试工具函数性能...");

		await performanceTest(() => {
			for (let i = 0; i < 1000; i++) {
				utils.createSuccessResponse("测试", { data: i });
			}
		}, "创建1000个成功响应");

		await performanceTest(() => {
			for (let i = 0; i < 1000; i++) {
				utils.validateRequired("test", "param");
			}
		}, "执行1000次参数验证");

		await performanceTest(() => {
			const testObj = { a: 1, b: { c: 2, d: [1, 2, 3] } };
			for (let i = 0; i < 100; i++) {
				utils.deepClone(testObj);
			}
		}, "执行100次深度克隆");

		// 测试配置函数性能
		console.log("2. 测试配置函数性能...");

		await performanceTest(() => {
			for (let i = 0; i < 1000; i++) {
				config.buildDidaApiUrl(`/test/endpoint/${i}`);
			}
		}, "构建1000个API URL");

		await performanceTest(() => {
			for (let i = 0; i < 1000; i++) {
				config.buildWeChatQRUrl(`state_${i}`);
			}
		}, "构建1000个微信二维码URL");

		console.log("性能基准测试完成");
	} catch (error) {
		console.error("性能基准测试失败:", error);
	}
}

/**
 * 运行所有测试
 */
async function runAllTests() {
	console.log("开始运行滴答清单API云对象测试...");
	console.log("测试时间:", new Date().toISOString());
	console.log("=".repeat(50));

	// 重置测试统计
	testStats.total = 0;
	testStats.passed = 0;
	testStats.failed = 0;
	testStats.errors = [];

	// 测试配置模块
	testConfig();
	console.log();

	// 测试工具函数
	testUtils();
	console.log();

	// 测试认证模块
	await testAuthModule();
	console.log();

	// 测试任务管理模块
	await testTasksModule();
	console.log();

	// 测试专注管理模块
	await testPomodoroModule();
	console.log();

	// 测试项目管理模块
	await testProjectsModule();
	console.log();

	// 测试习惯管理模块
	await testHabitsModule();
	console.log();

	// 测试统计服务模块
	await testStatisticsModule();
	console.log();

	// 测试用户服务模块
	await testUsersModule();
	console.log();

	// 性能基准测试
	await performanceBenchmark();
	console.log();

	// 输出测试结果统计
	console.log("=".repeat(50));
	console.log("📊 测试结果统计");
	console.log("=".repeat(50));
	console.log(`总测试数: ${testStats.total}`);
	console.log(`通过数: ${testStats.passed} ✅`);
	console.log(`失败数: ${testStats.failed} ❌`);
	console.log(
		`成功率: ${
			testStats.total > 0
				? Math.round((testStats.passed / testStats.total) * 100)
				: 0
		}%`
	);

	if (testStats.failed > 0) {
		console.log("\n❌ 失败的测试:");
		testStats.errors.forEach((error, index) => {
			console.log(`${index + 1}. ${error}`);
		});
	}

	console.log("\n=".repeat(50));
	console.log(
		testStats.failed === 0
			? "🎉 所有测试通过！"
			: "⚠️  部分测试失败，请检查上述错误"
	);
	console.log("=".repeat(50));
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
	runAllTests().catch(console.error);
}

/**
 * 测试任务管理模块
 */
async function testTasksModule() {
	console.log("=== 开始测试任务管理模块 ===");

	try {
		console.log("1. 测试任务管理模块加载...");
		const tasksModule = require("./modules/tasks");
		console.log("任务管理模块方法:", Object.keys(tasksModule));

		console.log("任务管理模块测试完成");
	} catch (error) {
		console.error("任务管理模块测试失败:", error);
	}
}

/**
 * 测试专注管理模块
 */
async function testPomodoroModule() {
	console.log("=== 开始测试专注管理模块 ===");

	try {
		console.log("1. 测试专注管理模块加载...");
		const pomodoroModule = require("./modules/pomodoro");
		console.log("专注管理模块方法:", Object.keys(pomodoroModule));

		console.log("专注管理模块测试完成");
	} catch (error) {
		console.error("专注管理模块测试失败:", error);
	}
}

/**
 * 测试项目管理模块
 */
async function testProjectsModule() {
	console.log("=== 开始测试项目管理模块 ===");

	try {
		console.log("1. 测试项目管理模块加载...");
		const projectsModule = require("./modules/projects");
		console.log("项目管理模块方法:", Object.keys(projectsModule));

		console.log("项目管理模块测试完成");
	} catch (error) {
		console.error("项目管理模块测试失败:", error);
	}
}

/**
 * 测试习惯管理模块
 */
async function testHabitsModule() {
	console.log("=== 开始测试习惯管理模块 ===");

	try {
		console.log("1. 测试习惯管理模块加载...");
		const habitsModule = require("./modules/habits");
		console.log("习惯管理模块方法:", Object.keys(habitsModule));

		console.log("习惯管理模块测试完成");
	} catch (error) {
		console.error("习惯管理模块测试失败:", error);
	}
}

/**
 * 测试统计服务模块
 */
async function testStatisticsModule() {
	console.log("=== 开始测试统计服务模块 ===");

	try {
		console.log("1. 测试统计服务模块加载...");
		const statisticsModule = require("./modules/statistics");
		console.log("统计服务模块方法:", Object.keys(statisticsModule));

		console.log("统计服务模块测试完成");
	} catch (error) {
		console.error("统计服务模块测试失败:", error);
	}
}

/**
 * 测试用户服务模块
 */
async function testUsersModule() {
	console.log("=== 开始测试用户服务模块 ===");

	try {
		console.log("1. 测试用户服务模块加载...");
		const usersModule = require("./modules/users");
		console.log("用户服务模块方法:", Object.keys(usersModule));

		console.log("用户服务模块测试完成");
	} catch (error) {
		console.error("用户服务模块测试失败:", error);
	}
}

module.exports = {
	testAuthModule,
	testUtils,
	testConfig,
	testTasksModule,
	testPomodoroModule,
	testProjectsModule,
	testHabitsModule,
	testStatisticsModule,
	testUsersModule,
	runAllTests,
};
