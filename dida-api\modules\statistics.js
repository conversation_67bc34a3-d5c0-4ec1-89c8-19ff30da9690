/**
 * 统计服务模块
 * 处理统计相关功能
 */

const {
	DIDA_API_BASE,
	DIDA_STATISTICS_APIS,
	REQUEST_CONFIG,
	buildDidaApiUrl,
} = require("../config");

const {
	logInfo,
	logError,
	createSuccessResponse,
	createErrorResponse,
	validateRequired,
	validateType,
	buildAuthHeaders,
	buildAuthCookies,
	safeJsonParse,
} = require("../utils");

/**
 * 获取用户排名统计
 * @param {string} authToken - 认证令牌
 * @param {string} csrfToken - CSRF令牌
 * @returns {object} 用户排名统计响应
 */
async function getUserRanking(authToken, csrfToken) {
	const methodName = "getUserRanking";
	logInfo(methodName, "开始获取用户排名统计");

	try {
		// 参数验证
		const authValidation = validateRequired(authToken, "authToken");
		if (authValidation) return authValidation;

		const csrfValidation = validateRequired(csrfToken, "csrfToken");
		if (csrfValidation) return csrfValidation;

		// 构建请求URL
		const apiUrl = buildDidaApiUrl(DIDA_STATISTICS_APIS.user_ranking);

		// 构建请求头和cookies
		const headers = buildAuthHeaders(authToken, csrfToken);
		const cookies = buildAuthCookies(authToken, csrfToken);

		logInfo(methodName, "发起获取用户排名统计请求", { apiUrl });

		// 发起请求
		const response = await uniCloud.httpclient.request(apiUrl, {
			method: "GET",
			headers: headers,
			cookies: cookies,
			timeout: REQUEST_CONFIG.timeout,
		});

		if (response.status !== 200) {
			throw new Error(`获取用户排名统计失败，状态码: ${response.status}`);
		}

		// 解析响应数据
		const responseData = safeJsonParse(response.data, null);

		if (!responseData) {
			throw new Error("无法解析用户排名统计数据");
		}

		logInfo(methodName, "成功获取用户排名统计");

		// 处理用户排名数据
		const processedData = {
			// 当前用户排名信息
			current_user: {
				rank: responseData.currentRank || 0,
				score: responseData.currentScore || 0,
				percentile: responseData.percentile || 0,
				beat_percentage: responseData.beatPercentage || 0,
			},
			// 排行榜信息
			leaderboard: {
				total_users: responseData.totalUsers || 0,
				top_users: responseData.topUsers || [],
				nearby_users: responseData.nearbyUsers || [],
			},
			// 统计周期
			period: {
				type: responseData.periodType || "weekly",
				start_date: responseData.startDate,
				end_date: responseData.endDate,
			},
			// 排名变化
			rank_change: {
				previous_rank: responseData.previousRank || 0,
				rank_delta: responseData.rankDelta || 0,
				is_improved: responseData.isImproved || false,
			},
		};

		return createSuccessResponse("获取用户排名统计成功", processedData);
	} catch (error) {
		logError(methodName, "获取用户排名统计失败", error);
		throw error;
	}
}

/**
 * 获取通用统计信息
 * @param {string} authToken - 认证令牌
 * @param {string} csrfToken - CSRF令牌
 * @returns {object} 通用统计信息响应
 */
async function getGeneralStatistics(authToken, csrfToken) {
	const methodName = "getGeneralStatistics";
	logInfo(methodName, "开始获取通用统计信息");

	try {
		// 参数验证
		const authValidation = validateRequired(authToken, "authToken");
		if (authValidation) return authValidation;

		const csrfValidation = validateRequired(csrfToken, "csrfToken");
		if (csrfValidation) return csrfValidation;

		// 构建请求URL
		const apiUrl = buildDidaApiUrl(DIDA_STATISTICS_APIS.general_statistics);

		// 构建请求头和cookies
		const headers = buildAuthHeaders(authToken, csrfToken);
		const cookies = buildAuthCookies(authToken, csrfToken);

		logInfo(methodName, "发起获取通用统计信息请求", { apiUrl });

		// 发起请求
		const response = await uniCloud.httpclient.request(apiUrl, {
			method: "GET",
			headers: headers,
			cookies: cookies,
			timeout: REQUEST_CONFIG.timeout,
		});

		if (response.status !== 200) {
			throw new Error(`获取通用统计信息失败，状态码: ${response.status}`);
		}

		// 解析响应数据
		const responseData = safeJsonParse(response.data, null);

		if (!responseData) {
			throw new Error("无法解析通用统计信息数据");
		}

		logInfo(methodName, "成功获取通用统计信息");

		// 处理通用统计数据
		const processedData = {
			// 概览统计
			overview: {
				total_tasks: responseData.totalTasks || 0,
				completed_tasks: responseData.completedTasks || 0,
				pending_tasks: responseData.pendingTasks || 0,
				completion_rate: responseData.completionRate || 0,
			},
			// 成就值统计
			achievement: {
				current_level: responseData.currentLevel || 0,
				current_points: responseData.currentPoints || 0,
				next_level_points: responseData.nextLevelPoints || 0,
				progress_percentage: responseData.progressPercentage || 0,
			},
			// 趋势统计
			trends: {
				daily_trend: responseData.dailyTrend || [],
				weekly_trend: responseData.weeklyTrend || [],
				monthly_trend: responseData.monthlyTrend || [],
			},
			// 活跃度统计
			activity: {
				active_days: responseData.activeDays || 0,
				total_days: responseData.totalDays || 0,
				activity_rate: responseData.activityRate || 0,
				longest_streak: responseData.longestStreak || 0,
				current_streak: responseData.currentStreak || 0,
			},
		};

		return createSuccessResponse("获取通用统计信息成功", processedData);
	} catch (error) {
		logError(methodName, "获取通用统计信息失败", error);
		throw error;
	}
}

// 导出统计服务模块函数
module.exports = {
	getUserRanking,
	getGeneralStatistics,
};
