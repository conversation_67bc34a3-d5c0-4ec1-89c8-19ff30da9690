/**
 * 任务管理模块
 * 处理任务获取、搜索等任务相关功能
 */

const {
	DIDA_API_BASE,
	DIDA_TASK_APIS,
	REQUEST_CONFIG,
	buildDidaApiUrl,
} = require("../config");

const {
	logInfo,
	logError,
	createSuccessResponse,
	createErrorResponse,
	validateRequired,
	validateType,
	buildAuthHeaders,
	buildAuthCookies,
	safeJsonParse,
} = require("../utils");

/**
 * 获取所有任务
 * @param {string} authToken - 认证令牌
 * @param {string} csrfToken - CSRF令牌
 * @returns {object} 任务列表响应
 */
async function getAllTasks(authToken, csrfToken) {
	const methodName = "getAllTasks";
	logInfo(methodName, "开始获取所有任务");

	try {
		// 参数验证
		const authValidation = validateRequired(authToken, "authToken");
		if (authValidation) return authValidation;

		const csrfValidation = validateRequired(csrfToken, "csrfToken");
		if (csrfValidation) return csrfValidation;

		// 构建请求URL
		const apiUrl = buildDidaApiUrl(DIDA_TASK_APIS.get_all_tasks);

		// 构建请求头和cookies
		const headers = buildAuthHeaders(authToken, csrfToken);
		const cookies = buildAuthCookies(authToken, csrfToken);

		logInfo(methodName, "发起获取所有任务请求", { apiUrl });

		// 发起请求
		const response = await uniCloud.httpclient.request(apiUrl, {
			method: "GET",
			headers: headers,
			cookies: cookies,
			timeout: REQUEST_CONFIG.timeout,
		});

		if (response.status !== 200) {
			throw new Error(`获取任务失败，状态码: ${response.status}`);
		}

		// 解析响应数据
		const responseData = safeJsonParse(response.data, null);

		if (!responseData) {
			throw new Error("无法解析任务数据");
		}

		logInfo(methodName, "成功获取所有任务", {
			taskCount: responseData.syncTaskBean?.update?.length || 0,
		});

		// 处理任务数据
		const tasks = responseData.syncTaskBean?.update || [];
		const processedTasks = tasks.map((task) => ({
			id: task.id,
			title: task.title,
			content: task.content,
			status: task.status,
			priority: task.priority,
			projectId: task.projectId,
			createdTime: task.createdTime,
			modifiedTime: task.modifiedTime,
			completedTime: task.completedTime,
			dueDate: task.dueDate,
			tags: task.tags || [],
			items: task.items || [],
		}));

		return createSuccessResponse("获取所有任务成功", {
			tasks: processedTasks,
			total: processedTasks.length,
			sync_info: {
				revision: responseData.revision,
				update_count: tasks.length,
			},
		});
	} catch (error) {
		logError(methodName, "获取所有任务失败", error);
		throw error;
	}
}

/**
 * 获取已完成任务（分页）
 * @param {string} authToken - 认证令牌
 * @param {string} csrfToken - CSRF令牌
 * @param {number} page - 页码，从0开始
 * @param {number} limit - 每页数量，默认50
 * @returns {object} 已完成任务列表响应
 */
async function getCompletedTasks(authToken, csrfToken, page = 0, limit = 50) {
	const methodName = "getCompletedTasks";
	logInfo(methodName, "开始获取已完成任务", { page, limit });

	try {
		// 参数验证
		const authValidation = validateRequired(authToken, "authToken");
		if (authValidation) return authValidation;

		const csrfValidation = validateRequired(csrfToken, "csrfToken");
		if (csrfValidation) return csrfValidation;

		const pageValidation = validateType(page, "number", "page");
		if (pageValidation) return pageValidation;

		const limitValidation = validateType(limit, "number", "limit");
		if (limitValidation) return limitValidation;

		// 构建请求URL（带分页参数）
		const apiUrl = `${buildDidaApiUrl(
			DIDA_TASK_APIS.get_completed_tasks
		)}?from=${page * limit}&limit=${limit}`;

		// 构建请求头和cookies
		const headers = buildAuthHeaders(authToken, csrfToken);
		const cookies = buildAuthCookies(authToken, csrfToken);

		logInfo(methodName, "发起获取已完成任务请求", { apiUrl });

		// 发起请求
		const response = await uniCloud.httpclient.request(apiUrl, {
			method: "GET",
			headers: headers,
			cookies: cookies,
			timeout: REQUEST_CONFIG.timeout,
		});

		if (response.status !== 200) {
			throw new Error(`获取已完成任务失败，状态码: ${response.status}`);
		}

		// 解析响应数据
		const responseData = safeJsonParse(response.data, null);

		if (!responseData) {
			throw new Error("无法解析已完成任务数据");
		}

		logInfo(methodName, "成功获取已完成任务", {
			taskCount: responseData.length || 0,
		});

		// 处理任务数据
		const tasks = Array.isArray(responseData) ? responseData : [];
		const processedTasks = tasks.map((task) => ({
			id: task.id,
			title: task.title,
			content: task.content,
			status: task.status,
			priority: task.priority,
			projectId: task.projectId,
			createdTime: task.createdTime,
			modifiedTime: task.modifiedTime,
			completedTime: task.completedTime,
			dueDate: task.dueDate,
			tags: task.tags || [],
			items: task.items || [],
		}));

		return createSuccessResponse("获取已完成任务成功", {
			tasks: processedTasks,
			pagination: {
				page: page,
				limit: limit,
				count: processedTasks.length,
				has_more: processedTasks.length === limit,
			},
		});
	} catch (error) {
		logError(methodName, "获取已完成任务失败", error);
		throw error;
	}
}

/**
 * 获取垃圾桶任务
 * @param {string} authToken - 认证令牌
 * @param {string} csrfToken - CSRF令牌
 * @param {number} page - 页码，从0开始
 * @param {number} limit - 每页数量，默认50
 * @returns {object} 垃圾桶任务列表响应
 */
async function getTrashTasks(authToken, csrfToken, page = 0, limit = 50) {
	const methodName = "getTrashTasks";
	logInfo(methodName, "开始获取垃圾桶任务", { page, limit });

	try {
		// 参数验证
		const authValidation = validateRequired(authToken, "authToken");
		if (authValidation) return authValidation;

		const csrfValidation = validateRequired(csrfToken, "csrfToken");
		if (csrfValidation) return csrfValidation;

		const pageValidation = validateType(page, "number", "page");
		if (pageValidation) return pageValidation;

		const limitValidation = validateType(limit, "number", "limit");
		if (limitValidation) return limitValidation;

		// 构建请求URL（带分页参数）
		const apiUrl = `${buildDidaApiUrl(DIDA_TASK_APIS.get_trash_tasks)}?from=${
			page * limit
		}&limit=${limit}`;

		// 构建请求头和cookies
		const headers = buildAuthHeaders(authToken, csrfToken);
		const cookies = buildAuthCookies(authToken, csrfToken);

		logInfo(methodName, "发起获取垃圾桶任务请求", { apiUrl });

		// 发起请求
		const response = await uniCloud.httpclient.request(apiUrl, {
			method: "GET",
			headers: headers,
			cookies: cookies,
			timeout: REQUEST_CONFIG.timeout,
		});

		if (response.status !== 200) {
			throw new Error(`获取垃圾桶任务失败，状态码: ${response.status}`);
		}

		// 解析响应数据
		const responseData = safeJsonParse(response.data, null);

		if (!responseData) {
			throw new Error("无法解析垃圾桶任务数据");
		}

		logInfo(methodName, "成功获取垃圾桶任务", {
			taskCount: responseData.length || 0,
		});

		// 处理任务数据
		const tasks = Array.isArray(responseData) ? responseData : [];
		const processedTasks = tasks.map((task) => ({
			id: task.id,
			title: task.title,
			content: task.content,
			status: task.status,
			priority: task.priority,
			projectId: task.projectId,
			createdTime: task.createdTime,
			modifiedTime: task.modifiedTime,
			deletedTime: task.deletedTime,
			dueDate: task.dueDate,
			tags: task.tags || [],
			items: task.items || [],
		}));

		return createSuccessResponse("获取垃圾桶任务成功", {
			tasks: processedTasks,
			pagination: {
				page: page,
				limit: limit,
				count: processedTasks.length,
				has_more: processedTasks.length === limit,
			},
		});
	} catch (error) {
		logError(methodName, "获取垃圾桶任务失败", error);
		throw error;
	}
}

/**
 * 搜索任务
 * @param {string} authToken - 认证令牌
 * @param {string} csrfToken - CSRF令牌
 * @param {string} keyword - 搜索关键词
 * @param {number} limit - 结果数量限制，默认20
 * @returns {object} 搜索结果响应
 */
async function searchTasks(authToken, csrfToken, keyword, limit = 20) {
	const methodName = "searchTasks";
	logInfo(methodName, "开始搜索任务", { keyword, limit });

	try {
		// 参数验证
		const authValidation = validateRequired(authToken, "authToken");
		if (authValidation) return authValidation;

		const csrfValidation = validateRequired(csrfToken, "csrfToken");
		if (csrfValidation) return csrfValidation;

		const keywordValidation = validateRequired(keyword, "keyword");
		if (keywordValidation) return keywordValidation;

		const limitValidation = validateType(limit, "number", "limit");
		if (limitValidation) return limitValidation;

		// 构建请求URL（带搜索参数）
		const apiUrl = `${buildDidaApiUrl(
			DIDA_TASK_APIS.task_search
		)}?keyword=${encodeURIComponent(keyword)}&limit=${limit}`;

		// 构建请求头和cookies
		const headers = buildAuthHeaders(authToken, csrfToken);
		const cookies = buildAuthCookies(authToken, csrfToken);

		logInfo(methodName, "发起搜索任务请求", { apiUrl });

		// 发起请求
		const response = await uniCloud.httpclient.request(apiUrl, {
			method: "GET",
			headers: headers,
			cookies: cookies,
			timeout: REQUEST_CONFIG.timeout,
		});

		if (response.status !== 200) {
			throw new Error(`搜索任务失败，状态码: ${response.status}`);
		}

		// 解析响应数据
		const responseData = safeJsonParse(response.data, null);

		if (!responseData) {
			throw new Error("无法解析搜索结果数据");
		}

		logInfo(methodName, "成功搜索任务", {
			resultCount: responseData.length || 0,
		});

		// 处理搜索结果
		const tasks = Array.isArray(responseData) ? responseData : [];
		const processedTasks = tasks.map((task) => ({
			id: task.id,
			title: task.title,
			content: task.content,
			status: task.status,
			priority: task.priority,
			projectId: task.projectId,
			createdTime: task.createdTime,
			modifiedTime: task.modifiedTime,
			completedTime: task.completedTime,
			dueDate: task.dueDate,
			tags: task.tags || [],
			items: task.items || [],
		}));

		return createSuccessResponse("搜索任务成功", {
			tasks: processedTasks,
			keyword: keyword,
			total: processedTasks.length,
			limit: limit,
		});
	} catch (error) {
		logError(methodName, "搜索任务失败", error);
		throw error;
	}
}

// 导出任务管理模块函数
module.exports = {
	getAllTasks,
	getCompletedTasks,
	getTrashTasks,
	searchTasks,
};
