# 部署到 GitHub Pages 的工作流
name: Deploy VitePress site to Pages

on:
  # 在推送到 main 分支时触发
  push:
    branches: [main]
  # 允许手动触发工作流
  workflow_dispatch:

# 设置 GITHUB_TOKEN 的权限，允许部署到 GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# 只允许一个并发部署，跳过正在运行和最新队列之间的运行队列
# 但是，不要取消正在进行的运行，因为我们希望允许这些生产部署完成
concurrency:
  group: pages
  cancel-in-progress: false

jobs:
  # 构建工作
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # 如果未启用 lastUpdated，则不需要
      
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18
      
      - name: Setup Pages
        uses: actions/configure-pages@v4
        with:
          enablement: true
      
      - name: Clean and install dependencies
        run: |
          cd frontend
          rm -rf node_modules package-lock.json
          npm install --no-optional
          npm install
      
      - name: Build with VitePress
        run: |
          cd frontend
          npm run docs:build
      
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: frontend/docs/.vitepress/dist

  # 部署工作
  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    needs: build
    runs-on: ubuntu-latest
    name: Deploy
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
