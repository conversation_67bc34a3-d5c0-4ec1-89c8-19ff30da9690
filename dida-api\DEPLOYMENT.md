# 滴答清单 API 云对象部署指南

## 概述

本文档详细说明了如何将滴答清单 API 云对象部署到 uniCloud 平台，包括环境准备、配置设置、部署流程和运维管理。

## 环境要求

### 1. 开发环境

- **Node.js**: >= 14.0.0
- **HBuilderX**: 最新版本
- **uniCloud**: 支持阿里云或腾讯云
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+

### 2. uniCloud 服务空间

- 已创建的 uniCloud 服务空间
- 开通云对象服务
- 配置适当的资源规格

## 部署前准备

### 1. 项目结构检查

确保项目结构符合 uniCloud 云对象规范：

```
dida-api/
├── index.obj.js          # 云对象主文件
├── config.js             # 配置文件
├── utils.js              # 工具函数
├── modules/              # 功能模块
│   ├── auth.js
│   ├── tasks.js
│   ├── pomodoro.js
│   ├── projects.js
│   ├── habits.js
│   ├── statistics.js
│   └── users.js
├── package.json          # 依赖配置
├── README.md             # 项目说明
├── DEPLOYMENT.md         # 部署指南
├── PERFORMANCE.md        # 性能优化指南
└── test.js              # 测试脚本
```

### 2. 依赖包检查

检查 package.json 中的依赖是否完整：

```json
{
	"name": "dida-api",
	"version": "1.0.0",
	"description": "滴答清单API云对象",
	"main": "index.obj.js",
	"dependencies": {},
	"engines": {
		"node": ">=14.0.0"
	}
}
```

### 3. 配置文件检查

确保 config.js 中的配置正确：

- API 端点 URL
- 微信配置参数
- 错误代码定义
- 请求超时设置

## 部署流程

### 1. 创建云对象

#### 1.1 在 HBuilderX 中创建

1. 打开 HBuilderX
2. 创建新的 uniCloud 项目或在现有项目中添加
3. 在 uniCloud/cloudfunctions 目录下创建 dida-api 文件夹
4. 将所有源代码文件复制到该文件夹

#### 1.2 配置云对象

在 HBuilderX 中右键点击 dida-api 文件夹，选择"创建云对象"

### 2. 上传部署

#### 2.1 首次部署

1. 右键点击 dida-api 文件夹
2. 选择"上传并运行"
3. 选择目标服务空间
4. 等待部署完成

#### 2.2 更新部署

1. 修改代码后保存
2. 右键点击 dida-api 文件夹
3. 选择"上传并运行"
4. 确认更新

### 3. 配置云对象参数

#### 3.1 内存配置

根据实际需求配置云对象内存：

- **开发环境**: 512MB
- **测试环境**: 1024MB
- **生产环境**: 2048MB 或更高

#### 3.2 超时配置

设置合理的超时时间：

- **HTTP 请求超时**: 30 秒
- **云对象执行超时**: 60 秒

#### 3.3 并发配置

控制并发执行数量：

- **开发环境**: 10 个并发
- **生产环境**: 100 个并发

## 环境配置

### 1. 开发环境

```javascript
// 开发环境配置
const DEV_CONFIG = {
	logLevel: "DEBUG",
	enableCache: false,
	requestTimeout: 10000,
	retryTimes: 1,
};
```

### 2. 测试环境

```javascript
// 测试环境配置
const TEST_CONFIG = {
	logLevel: "INFO",
	enableCache: true,
	requestTimeout: 20000,
	retryTimes: 2,
};
```

### 3. 生产环境

```javascript
// 生产环境配置
const PROD_CONFIG = {
	logLevel: "WARN",
	enableCache: true,
	requestTimeout: 30000,
	retryTimes: 3,
};
```

## 测试验证

### 1. 功能测试

部署完成后，运行测试脚本验证功能：

```javascript
// 在云函数控制台中运行
const result = await uniCloud.callFunction({
	name: "dida-api",
	data: {
		action: "getWeChatQRCode",
		params: {},
	},
});
console.log("测试结果:", result);
```

### 2. 性能测试

使用性能测试工具验证响应时间：

```javascript
// 性能测试示例
const startTime = Date.now();
const result = await uniCloud.callFunction({
	name: "dida-api",
	data: {
		action: "getAllTasks",
		params: {},
	},
});
const responseTime = Date.now() - startTime;
console.log("响应时间:", responseTime, "ms");
```
